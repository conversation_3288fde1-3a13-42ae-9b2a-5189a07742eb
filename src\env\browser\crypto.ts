import { base64 } from './base64';
import { md5 as _md5 } from './md5';

export function getNonce(): string {
	return base64(globalThis.crypto.getRandomValues(new Uint8Array(16)));
}

export function md5(data: string, encoding: 'base64' | 'hex' = 'hex'): string {
	return _md5(data, encoding);
}

export async function sha256(data: string, encoding: 'base64' | 'hex' = 'hex'): Promise<string> {
	const encoder = new TextEncoder();
	const dataBuffer = encoder.encode(data);
	const hashBuffer = await globalThis.crypto.subtle.digest('SHA-256', dataBuffer);
	const hashArray = new Uint8Array(hashBuffer);

	if (encoding === 'base64') {
		return base64(hashArray);
	}

	// Convert to hex
	return Array.from(hashArray)
		.map(b => b.toString(16).padStart(2, '0'))
		.join('');
}

export function uuid(): string {
	return globalThis.crypto.randomUUID();
}
